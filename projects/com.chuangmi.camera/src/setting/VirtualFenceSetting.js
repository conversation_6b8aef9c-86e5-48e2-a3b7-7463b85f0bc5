import {View, StyleSheet, ScrollView, Text} from 'react-native';
import React, {useEffect, useState} from 'react';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {IMIGotoPage} from '../../../../imilab-rn-sdk';
import {imiThemeManager, showToast, showLoading} from '../../../../imilab-design-ui';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import {stringsTo} from '../../../../globalization/Localize';
import {LetDevice} from '../../../../imilab-rn-sdk/native/iot-kit/IMIDevice';

// 虚拟围栏物模型ID常量
const PROPERTY_IDS = {
  VIRTUAL_FENCE_DETECTION: '100000',  // 虚拟围栏检测开关
  PERSON_ENTER_FENCE: '100002',       // 有人进入围栏开关
  PERSON_LEAVE_FENCE: '100003',       // 有人离开围栏开关
  SHOW_VIRTUAL_FENCE: '100004',       // 显示虚拟围栏
};

const VirtualFenceSetting = props => {
  // 状态管理
  const [virtualFenceEnabled, setVirtualFenceEnabled] = useState(false);
  const [personEnterFence, setPersonEnterFence] = useState(false);
  const [personLeaveFence, setPersonLeaveFence] = useState(false);
  const [showVirtualFence, setShowVirtualFence] = useState(false);
  const [fenceAreaData, setFenceAreaData] = useState([30, 20, 250, 150]);
  const [isLoading, setIsLoading] = useState(true);

  // 获取所有虚拟围栏相关的物模型数据
  const loadVirtualFenceSettings = async () => {
    try {
      setIsLoading(true);
      console.log('开始获取虚拟围栏设置...');

      // 并行获取所有物模型属性
      const promises = [
        LetDevice.getProperties(true, LetDevice.deviceID, PROPERTY_IDS.VIRTUAL_FENCE_DETECTION),
        LetDevice.getProperties(true, LetDevice.deviceID, PROPERTY_IDS.PERSON_ENTER_FENCE),
        LetDevice.getProperties(true, LetDevice.deviceID, PROPERTY_IDS.PERSON_LEAVE_FENCE),
        LetDevice.getProperties(true, LetDevice.deviceID, PROPERTY_IDS.SHOW_VIRTUAL_FENCE),
      ];

      const results = await Promise.allSettled(promises);

      // 检查是否有任何请求失败
      const hasFailures = results.some(result => result.status === 'rejected');
      if (hasFailures) {
        throw new Error('部分物模型数据获取失败');
      }

      // 解析虚拟围栏检测开关
      if (results[0].status === 'fulfilled' && results[0].value?.value !== undefined) {
        const value = parseInt(results[0].value.value);
        setVirtualFenceEnabled(value === 1);
        console.log('虚拟围栏检测开关:', value);
      }

      // 解析有人进入围栏开关
      if (results[1].status === 'fulfilled' && results[1].value?.value !== undefined) {
        const value = parseInt(results[1].value.value);
        setPersonEnterFence(value === 1);
        console.log('有人进入围栏开关:', value);
      }

      // 解析有人离开围栏开关
      if (results[2].status === 'fulfilled' && results[2].value?.value !== undefined) {
        const value = parseInt(results[2].value.value);
        setPersonLeaveFence(value === 1);
        console.log('有人离开围栏开关:', value);
      }

      // 解析显示虚拟围栏开关
      if (results[3].status === 'fulfilled' && results[3].value?.value !== undefined) {
        const value = parseInt(results[3].value.value);
        setShowVirtualFence(value === 1);
        console.log('显示虚拟围栏开关:', value);
      }

      console.log('虚拟围栏设置加载完成');
    } catch (error) {
      console.error('获取虚拟围栏设置失败:', error);
      showToast(stringsTo('commLoadingFailText'));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadVirtualFenceSettings();
  }, []);

  // 设置物模型属性的通用函数
  const setVirtualFenceProperty = async (propertyId, value, stateSetter, propertyName) => {
    try {
      showLoading(stringsTo('commWaitText'), true);
      console.log(`设置${propertyName}:`, value);

      const paramValue = value ? 1 : 0;
      const paramJson = JSON.stringify({ value: paramValue });

      await LetDevice.setProperties(true, LetDevice.deviceID, propertyId, paramJson);

      // 设置成功后更新本地状态
      stateSetter(value);
      showToast(stringsTo('settings_set_success'));
      console.log(`${propertyName}设置成功:`, paramValue);

    } catch (error) {
      console.error(`设置${propertyName}失败:`, error);
      showToast(stringsTo('operationFailed'));
      // 设置失败时恢复原状态
      stateSetter(!value);
    } finally {
      showLoading(false);
    }
  };

  const handleVirtualFenceChange = (value) => {
    setVirtualFenceProperty(
      PROPERTY_IDS.VIRTUAL_FENCE_DETECTION,
      value,
      setVirtualFenceEnabled,
      '虚拟围栏检测开关'
    );
  };

  const handlePersonEnterChange = (value) => {
    setVirtualFenceProperty(
      PROPERTY_IDS.PERSON_ENTER_FENCE,
      value,
      setPersonEnterFence,
      '有人进入围栏开关'
    );
  };

  const handlePersonLeaveChange = (value) => {
    setVirtualFenceProperty(
      PROPERTY_IDS.PERSON_LEAVE_FENCE,
      value,
      setPersonLeaveFence,
      '有人离开围栏开关'
    );
  };

  const handleShowVirtualFenceChange = (value) => {
    setVirtualFenceProperty(
      PROPERTY_IDS.SHOW_VIRTUAL_FENCE,
      value,
      setShowVirtualFence,
      '显示虚拟围栏开关'
    );
  };

  const handleFenceAreaPress = () => {
    // 跳转到围栏区域设置页面
    console.log('跳转到围栏区域设置');
    // 模拟隐私区域数据（应该在围栏内部）
    const privacyAreaData = [50, 40, 200, 120];

    props.navigation.push('VirtualFenceAreaSetting', {
      areaData: fenceAreaData, // 围栏区域坐标
      privateSwitch: true, // 隐私区域开关
      privateAreaData: privacyAreaData, // 隐私区域坐标
      styleType: 2, // 可爱2样式
      callback: (areaData, areaType) => {
        console.log('围栏区域编辑回调:', areaData, areaType);
        setFenceAreaData(areaData); // 更新围栏区域数据
        // TODO: 这里可以添加保存围栏区域到物模型的逻辑
      }
    });
  };

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <View style={styles.container}>
        <NavigationBar
          title={stringsTo("virtual_Fence")}
          left={[
            {
              key: NavigationBar.ICON.BACK,
              onPress: () => {
                props.navigation.goBack() ? props.navigation.goBack() : IMIGotoPage.exit();
              },
              accessibilityLabel: 'virtual_fence_back',
            },
          ]}
          right={[]}
        />
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>{stringsTo('commWaitText')}</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <NavigationBar
        title={stringsTo("virtual_Fence")}
        left={[
          {
            key: NavigationBar.ICON.BACK,
            onPress: () => {
              props.navigation.goBack() ? props.navigation.goBack() : IMIGotoPage.exit();
            },
            accessibilityLabel: 'virtual_fence_back',
          },
        ]}
        right={[]}
      />
      
      <ScrollView style={styles.scrollView}>
        {/* 设置选项区域 */}
        <View style={styles.settingsSection}>
          {/* 虚拟围栏主开关 */}
          <ListItmeWithSwitch 
            title="虚拟围栏"
            value={virtualFenceEnabled}
            onValueChange={handleVirtualFenceChange}
            accessibilityLabel={['virtual_fence_off', 'virtual_fence_on']}
          />
          
          {/* 有人进入围栏 */}
          {virtualFenceEnabled && (
            <ListItmeWithSwitch 
              title="有人进入围栏"
              subtitle="当有人进入围栏时，拍摄视频并推送"
              value={personEnterFence}
              onValueChange={handlePersonEnterChange}
              accessibilityLabel={['person_enter_off', 'person_enter_on']}
            />
          )}
          
          {/* 有人离开围栏 */}
          {virtualFenceEnabled && (
            <ListItmeWithSwitch 
              title="有人离开围栏"
              subtitle="当有人离开围栏时，拍摄视频并推送"
              value={personLeaveFence}
              onValueChange={handlePersonLeaveChange}
              accessibilityLabel={['person_leave_off', 'person_leave_on']}
            />
          )}
          
          {/* 围栏区域设置 */}
          {virtualFenceEnabled && (
            <ListItem
              title="围栏区域"
              subtitle={`已设置区域: ${Math.round((fenceAreaData[2] - fenceAreaData[0]) * (fenceAreaData[3] - fenceAreaData[1]) / 10000 * 100)}% 画面`}
              onPress={handleFenceAreaPress}
              accessibilityLabel="fence_area_setting"
            />
          )}
          
          {/* 显示虚拟围栏 */}
          {virtualFenceEnabled && (
            <ListItmeWithSwitch 
              title="显示虚拟围栏"
              value={showVirtualFence}
              onValueChange={handleShowVirtualFenceChange}
              accessibilityLabel={['show_fence_off', 'show_fence_on']}
            />
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg || '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  settingsSection: {
    marginTop: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: imiThemeManager.theme.textColor || '#666666',
  },
});

export default VirtualFenceSetting;
